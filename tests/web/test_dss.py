from playwright.sync_api import Page, expect


def test_example1(page: Page) -> None:
    page.goto("http://10.12.135.167:9089/#/login")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("ROOT")
    page.get_by_placeholder("请输入密码").click()
    page.get_by_placeholder("请输入密码").fill("sA123456@")
    page.get_by_role("button", name="登录").click()
    expect(page.locator(".frame-vlayout-nav-logo")).to_be_visible()


def test_example2(page: Page) -> None:
    page.goto("http://10.12.135.167:9089/#/projectmanage")
    page.get_by_role("button", name=" 新建项目").click()
    page.get_by_placeholder("请输入项目名称").click()
    page.get_by_placeholder("请输入项目名称").fill("1234")
    page.get_by_label("场景类型").click()
    page.get_by_role("listitem").nth(1).click()
    page.get_by_role("button", name="确定").click()
    expect(page.get_by_text("1234")).to_be_visible()
