# pytest.ini 配置清理任务

## 任务背景
用户发现 pytest.ini 和 Dashboard 中存在重复的 HTML 报告配置，造成配置冲突。

## 问题分析
- **pytest.ini**: `--html=reports/report.html` (固定路径)
- **Dashboard**: `--html=reports/test_report_{timestamp}.html` (动态路径)
- **冲突**: 命令行参数覆盖配置文件参数，导致 pytest.ini 配置无效

## 解决方案
采用方案1：清理 pytest.ini，移除重复配置

## 执行步骤
1. ✅ 移除 pytest.ini 中的 `--html=reports/report.html` 配置
2. ✅ 保留基础配置（testpaths, markers, 其他选项）
3. ✅ 验证 Dashboard 报告功能正常
4. ✅ 完全删除 pytest.ini 文件（用户只使用 Dashboard）

## 修改内容

### pytest.ini 变更
```diff
[pytest]
testpaths = tests
addopts =
-    --html=reports/report.html
    --self-contained-html
    --tb=short
    -v
markers =
    web: Web UI tests
    api: API tests
    smoke: Smoke tests
    regression: Regression tests
```

## 预期效果
- ✅ 消除配置冲突
- ✅ Dashboard 成为统一配置中心
- ✅ 为动态文件夹功能打下基础
- ✅ 简化项目结构，移除冗余配置文件

## 验证方法
1. 运行 Dashboard 测试，确认报告正常生成
2. 检查报告文件命名格式：`test_report_{timestamp}.html`
3. 确认无配置冲突错误

## 完成时间
2025-07-31

## 最终决策
用户选择完全删除 pytest.ini，因为只通过 Dashboard 使用测试功能。

## 状态
✅ 已完成 - pytest.ini 已完全删除
