# 统一 Python 环境配置修复

## 任务背景
用户指出所有命令执行都需要使用特定的 conda 环境：`D:/Anaconda3/envs/py310/python.exe`，而不是系统默认的 Python 解释器。

## 问题分析
项目中多个地方使用了不同的 Python 解释器调用方式：
1. `sys.executable` - 使用当前运行的 Python 解释器
2. 直接调用命令如 `pytest` - 依赖系统 PATH
3. 硬编码路径 - 不统一，难以维护

这导致在 conda 虚拟环境中可能使用错误的 Python 解释器。

## 解决方案
创建统一的 Python 环境配置，确保所有命令都使用指定的 py310 环境。

## 修改内容

### 1. start_dashboard.py 修改

#### 添加统一配置（第9-10行）
```python
# 统一的 Python 环境配置
PYTHON_PATH = "D:/Anaconda3/envs/py310/python.exe"
```

#### 修改 install_dependencies 函数（第33-42行）
**修改前**：
```python
cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
```

**修改后**：
```python
cmd = [PYTHON_PATH, "-m", "pip", "install", "-r", "requirements.txt"]
```

#### 修改 start_dashboard 函数（第58-65行）
**修改前**：
```python
subprocess.run([
    sys.executable, "-m", "streamlit", "run",
    "test_dashboard.py",
    "--server.port", "8501",
    "--server.address", "localhost",
    "--server.headless", "true"
], encoding='utf-8')
```

**修改后**：
```python
subprocess.run([
    PYTHON_PATH, "-m", "streamlit", "run",
    "test_dashboard.py",
    "--server.port", "8501",
    "--server.address", "localhost",
    "--server.headless", "true"
], encoding='utf-8')
```

### 2. test_dashboard.py 修改

#### 添加统一配置（第13-14行）
```python
# 统一的 Python 环境配置
PYTHON_PATH = "D:/Anaconda3/envs/py310/python.exe"
```

#### 修改 run_test 函数（第71-72行）
**修改前**：
```python
cmd = ["pytest", test_path]
```

**修改后**：
```python
cmd = [PYTHON_PATH, "-m", "pytest", test_path]
```

#### 修改 run_codegen 函数（第112-113行）
**修改前**：
```python
python_path = "D:/Anaconda3/envs/py310/python.exe"
cmd = [python_path, "-m", "playwright", "codegen"]
```

**修改后**：
```python
cmd = [PYTHON_PATH, "-m", "playwright", "codegen"]
```

## 验证结果

通过测试脚本验证所有命令：

### 测试结果
- ✅ **pytest 命令**：`pytest 8.4.1`
- ✅ **streamlit 命令**：`Streamlit, version 1.47.1`
- ✅ **playwright 命令**：`Version 1.40.0`
- ✅ **pip 命令**：`pip 25.1.1 from D:\Anaconda3\envs\py310\lib\site-packages\pip (python 3.10)`

所有命令都正确使用了 py310 环境的 Python 解释器。

## 优势

1. **统一性**：所有命令都使用相同的 Python 环境
2. **可维护性**：只需修改一个配置常量即可更换环境
3. **一致性**：避免了环境不一致导致的问题
4. **可靠性**：确保在正确的虚拟环境中执行所有操作

## 影响范围

- ✅ **依赖安装**：pip 使用正确环境
- ✅ **控制台启动**：streamlit 使用正确环境
- ✅ **测试执行**：pytest 使用正确环境
- ✅ **代码生成**：playwright codegen 使用正确环境

## 最终效果

项目现在完全使用指定的 py310 conda 环境，确保所有功能在一致的环境中运行，避免了环境混乱导致的各种问题。
