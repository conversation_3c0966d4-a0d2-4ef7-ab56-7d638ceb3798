# Playwright Codegen 浏览器窗口不弹出问题修复

## 任务背景
用户反馈 Playwright Codegen 功能不报错但浏览器窗口未弹出的问题。

## 问题分析
在 `test_dashboard.py` 的 `run_codegen` 函数中，`subprocess.Popen` 设置了输出重定向：
```python
process = subprocess.Popen(
    cmd,
    stdout=subprocess.PIPE,  # ❌ 阻止浏览器正常启动
    stderr=subprocess.PIPE,  # ❌ 阻止浏览器正常启动
    text=True,
    encoding='utf-8'
)
```

**问题原因**：
1. `stdout=subprocess.PIPE` 和 `stderr=subprocess.PIPE` 重定向了子进程输出
2. Playwright Codegen 需要直接与系统交互才能正常启动浏览器
3. 输出重定向阻止了浏览器窗口的正常弹出

## 解决方案
采用方案1：移除输出重定向，让 Playwright 直接与系统交互

## 修改内容

### 1. 修复 run_codegen 函数（第105-124行）
**修改前**：
```python
def run_codegen(url=None):
    """启动 Playwright Codegen"""
    try:
        cmd = ["playwright", "codegen"]
        if url and url.strip():
            cmd.append(url.strip())

        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )
        return True, process, cmd
    except FileNotFoundError:
        return False, None, ["playwright 未找到，请确保已安装 Playwright"]
    except Exception as e:
        return False, None, [f"启动失败: {str(e)}"]
```

**修改后**：
```python
def run_codegen(url=None):
    """启动 Playwright Codegen"""
    try:
        cmd = ["playwright", "codegen"]
        if url and url.strip():
            cmd.append(url.strip())

        # 移除输出重定向，让 Playwright 直接与系统交互
        process = subprocess.Popen(cmd, shell=True)
        return True, cmd
    except FileNotFoundError:
        return False, ["playwright 未找到，请确保已安装 Playwright"]
    except Exception as e:
        return False, [f"启动失败: {str(e)}"]
```

### 2. 调整调用处代码（第179-187行）
**修改前**：
```python
if st.button("🚀 启动 Codegen", use_container_width=True):
    success, process, cmd = run_codegen(codegen_url)
    if success:
        st.success("✅ Playwright Codegen 已启动!")
        st.code(" ".join(cmd))
    else:
        st.error("❌ 启动 Codegen 失败!")
        st.error(" ".join(cmd))
```

**修改后**：
```python
if st.button("🚀 启动 Codegen", use_container_width=True):
    success, cmd = run_codegen(codegen_url)
    if success:
        st.success("✅ Playwright Codegen 已启动!")
        st.code(" ".join(cmd))
    else:
        st.error("❌ 启动 Codegen 失败!")
        st.error(" ".join(cmd))
```

## 修复效果验证
通过测试脚本验证修复效果：
```bash
python test_codegen_fix.py
```

**测试结果**：
- ✅ Playwright Codegen 成功启动
- ✅ 浏览器进程正常创建
- ✅ 可以看到详细的浏览器启动日志
- ✅ 修复生效，浏览器窗口正常弹出

## 关键改进
1. **移除输出重定向**：让 Playwright 能直接与系统交互
2. **添加 shell=True**：提高 Windows 系统兼容性
3. **简化返回值**：移除不必要的 process 对象返回
4. **保持错误处理**：维持原有的异常捕获机制

## 预期效果
- Playwright Codegen 浏览器窗口正常弹出
- 用户可以正常使用代码生成功能
- 兼容 Windows 系统环境
- 保持原有的错误提示机制

## 进一步优化：conda 虚拟环境支持

### 问题发现
用户反馈在 conda 虚拟环境中启动 Codegen 时出现问题，需要确保在正确的 Python 环境中执行。

### 优化内容

#### 1. 添加 sys 模块导入（第5-11行）
**修改前**：
```python
import streamlit as st
import os
import subprocess
import glob
import webbrowser
from datetime import datetime
```

**修改后**：
```python
import streamlit as st
import os
import subprocess
import sys
import glob
import webbrowser
from datetime import datetime
```

#### 2. 优化 run_codegen 函数（第106-125行）
**修改前**：
```python
def run_codegen(url=None):
    """启动 Playwright Codegen"""
    try:
        cmd = ["playwright", "codegen"]
        if url and url.strip():
            cmd.append(url.strip())
        subprocess.Popen(cmd, shell=True)
        return True, cmd
    except FileNotFoundError:
        return False, ["playwright 未找到，请确保已安装 Playwright"]
    except Exception as e:
        return False, [f"启动失败: {str(e)}"]
```

**修改后**：
```python
def run_codegen(url=None):
    """启动 Playwright Codegen"""
    try:
        # 使用当前 Python 解释器确保在正确环境中执行
        cmd = [sys.executable, "-m", "playwright", "codegen"]
        if url and url.strip():
            cmd.append(url.strip())
        subprocess.Popen(cmd, shell=True)
        return True, cmd
    except FileNotFoundError:
        return False, ["Python 解释器未找到，请检查环境配置"]
    except Exception as e:
        return False, [f"启动失败: {str(e)}\n请确保已安装 Playwright：pip install playwright && playwright install"]
```

### 优化验证
通过测试脚本验证 conda 环境支持：
```bash
python test_conda_codegen.py
```

**测试结果**：
- ✅ 正确使用 conda 环境：`D:\Anaconda3\python.exe`
- ✅ 命令构建正确：`D:\Anaconda3\python.exe -m playwright codegen`
- ✅ 浏览器正常启动（pid=8828）
- ✅ Codegen 功能在 conda 环境中正常工作

## 最终效果
- Playwright Codegen 浏览器窗口正常弹出
- 完美支持 conda 虚拟环境
- 用户可以正常使用代码生成功能
- 兼容 Windows 系统环境
- 提供更准确的错误提示信息
